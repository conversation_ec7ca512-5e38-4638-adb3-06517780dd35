// 功能测试脚本：验证节点和连线删除功能
console.log('=== 删除功能测试开始 ===');

// 检查当前状态
function checkCurrentState() {
  const editButton = document.querySelector('button[title*="编辑"]');
  const isInEditMode = editButton && editButton.textContent.includes('退出');
  const edges = document.querySelectorAll('.react-flow__edge');
  const nodes = document.querySelectorAll('.react-flow__node');
  const deleteEdgeButton = document.querySelector('button[title*="删除选中的连线"]');
  const deleteNodeButton = document.querySelector('button[title*="删除选中的节点"]');
  const addNodeButton = document.querySelector('button[title*="直接在画布中心添加节点"]');

  console.log('当前状态:', {
    isInEditMode,
    nodeCount: nodes.length,
    edgeCount: edges.length,
    hasDeleteEdgeButton: !!deleteEdgeButton,
    hasDeleteNodeButton: !!deleteNodeButton,
    hasAddNodeButton: !!addNodeButton
  });

  return {
    isInEditMode,
    edges,
    nodes,
    deleteEdgeButton,
    deleteNodeButton,
    addNodeButton
  };
}

// 进入编辑模式
function enterEditMode() {
  const editButton = document.querySelector('button[title*="编辑"]');
  if (editButton && !editButton.textContent.includes('退出')) {
    console.log('进入编辑模式...');
    editButton.click();
    return true;
  }
  return false;
}

// 测试连线选择和删除
function testEdgeSelection() {
  const edges = document.querySelectorAll('.react-flow__edge');
  if (edges.length > 0) {
    console.log(`找到 ${edges.length} 条连线，测试选择第一条...`);
    const firstEdge = edges[0];

    // 模拟点击连线
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    firstEdge.dispatchEvent(clickEvent);

    setTimeout(() => {
      const deleteButton = document.querySelector('button[title*="删除选中的连线"]');
      if (deleteButton) {
        console.log('✓ 连线选择成功！删除按钮出现');
        console.log('测试删除功能...');

        const edgeCountBefore = document.querySelectorAll('.react-flow__edge').length;
        deleteButton.click();

        setTimeout(() => {
          const edgeCountAfter = document.querySelectorAll('.react-flow__edge').length;
          const deleteButtonAfter = document.querySelector('button[title*="删除选中的连线"]');

          if (edgeCountAfter < edgeCountBefore && !deleteButtonAfter) {
            console.log('✓ 连线删除成功！');
          } else {
            console.log('✗ 连线删除失败');
          }
        }, 1000);
      } else {
        console.log('✗ 连线选择失败，删除按钮未出现');
      }
    }, 500);
  } else {
    console.log('✗ 未找到连线');
  }
}

// 测试节点选择和删除
function testNodeSelection() {
  const nodes = document.querySelectorAll('.react-flow__node');
  if (nodes.length > 0) {
    console.log(`找到 ${nodes.length} 个节点，测试选择第一个...`);
    const firstNode = nodes[0];

    // 模拟点击节点
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    firstNode.dispatchEvent(clickEvent);

    setTimeout(() => {
      const deleteButton = document.querySelector('button[title*="删除选中的节点"]');
      if (deleteButton) {
        console.log('✓ 节点选择成功！删除按钮出现');
        console.log('测试删除功能...');

        const nodeCountBefore = document.querySelectorAll('.react-flow__node').length;
        deleteButton.click();

        setTimeout(() => {
          const nodeCountAfter = document.querySelectorAll('.react-flow__node').length;
          const deleteButtonAfter = document.querySelector('button[title*="删除选中的节点"]');

          if (nodeCountAfter < nodeCountBefore && !deleteButtonAfter) {
            console.log('✓ 节点删除成功！');
          } else {
            console.log('✗ 节点删除失败');
          }
        }, 1000);
      } else {
        console.log('✗ 节点选择失败，删除按钮未出现');
      }
    }, 500);
  } else {
    console.log('✗ 未找到节点');
  }
}

// 测试新增节点按钮
function testAddNodeButton() {
  const addNodeButton = document.querySelector('button[title*="直接在画布中心添加节点"]');
  if (addNodeButton) {
    console.log('测试新增节点按钮...');
    const nodeCountBefore = document.querySelectorAll('.react-flow__node').length;
    console.log('添加前节点数量:', nodeCountBefore);

    addNodeButton.click();

    setTimeout(() => {
      const nodeCountAfter = document.querySelectorAll('.react-flow__node').length;
      console.log('添加后节点数量:', nodeCountAfter);

      if (nodeCountAfter > nodeCountBefore) {
        console.log('✓ 节点添加成功！');
      } else {
        console.log('✗ 节点添加失败');
      }
    }, 1000);
  } else {
    console.log('✗ 未找到新增节点按钮');
  }
}

// 测试双击添加节点
function testDoubleClick() {
  const reactFlowPane = document.querySelector('.react-flow__pane');
  if (reactFlowPane) {
    console.log('测试双击添加节点...');
    const rect = reactFlowPane.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const event = new MouseEvent('dblclick', {
      bubbles: true,
      cancelable: true,
      clientX: centerX,
      clientY: centerY
    });

    reactFlowPane.dispatchEvent(event);
    console.log('双击事件已触发');
  } else {
    console.log('✗ 未找到画布');
  }
}

// 测试Delete键删除功能
function testDeleteKey() {
  console.log('测试Delete键删除功能...');

  // 先选择一个连线
  const edges = document.querySelectorAll('.react-flow__edge');
  if (edges.length > 0) {
    const firstEdge = edges[0];
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    firstEdge.dispatchEvent(clickEvent);

    setTimeout(() => {
      // 模拟按Delete键
      const deleteEvent = new KeyboardEvent('keydown', {
        key: 'Delete',
        code: 'Delete',
        bubbles: true
      });
      document.dispatchEvent(deleteEvent);

      setTimeout(() => {
        const edgesAfter = document.querySelectorAll('.react-flow__edge');
        if (edgesAfter.length < edges.length) {
          console.log('✓ Delete键删除连线成功！');
        } else {
          console.log('✗ Delete键删除连线失败');
        }
      }, 1000);
    }, 500);
  }
}

// 执行测试
const state = checkCurrentState();
if (!state.isInEditMode) {
  enterEditMode();
  setTimeout(() => {
    console.log('开始测试连线功能...');
    testEdgeSelection();
    setTimeout(() => {
      console.log('开始测试节点功能...');
      testNodeSelection();
      setTimeout(() => {
        console.log('开始测试新增节点...');
        testAddNodeButton();
        setTimeout(() => {
          console.log('开始测试双击添加...');
          testDoubleClick();
          setTimeout(() => {
            console.log('开始测试Delete键...');
            testDeleteKey();
          }, 2000);
        }, 2000);
      }, 3000);
    }, 3000);
  }, 1000);
} else {
  console.log('开始测试连线功能...');
  testEdgeSelection();
  setTimeout(() => {
    console.log('开始测试节点功能...');
    testNodeSelection();
    setTimeout(() => {
      console.log('开始测试新增节点...');
      testAddNodeButton();
      setTimeout(() => {
        console.log('开始测试双击添加...');
        testDoubleClick();
        setTimeout(() => {
          console.log('开始测试Delete键...');
          testDeleteKey();
        }, 2000);
      }, 2000);
    }, 3000);
  }, 3000);
}

console.log('');
console.log('=== 手动测试步骤 ===');
console.log('1. 确保在编辑模式下');
console.log('2. 点击任意连线，应该变红并显示删除按钮');
console.log('3. 点击"删除连线"按钮，连线应该消失');
console.log('4. 点击任意节点，应该选中并显示删除按钮');
console.log('5. 点击"删除节点"按钮，节点和相关连线应该消失');
console.log('6. 多选节点时会显示确认对话框');
console.log('7. 按Delete键可以删除选中的项目');
console.log('8. 连线选中时有视觉反馈（变红、虚线、阴影）');
