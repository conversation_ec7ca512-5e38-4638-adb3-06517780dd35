import React, { useCallback, useMemo, useState } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ConnectionMode,
  useReactFlow
} from 'reactflow';
import 'reactflow/dist/style.css';
import { toast } from 'sonner';
import { useAppStore } from '../store/useAppStore';
import { nodeTypes } from './FlowNode';
import { FlowNode as FlowNodeType } from '../types';
import { RotateCcw, Plus, Trash2, Link, Unlink } from 'lucide-react';

interface FlowChartProps {
  className?: string;
}

export const FlowChart: React.FC<FlowChartProps> = ({ className = '' }) => {
  const { 
    nodes: storeNodes, 
    edges: storeEdges, 
    getVisibleNodes, 
    isEditMode,
    currentUser,
    updateNodePosition,
    resetNodePositions,
    addNode,
    deleteNode,
    addEdge: storeAddEdge,
    deleteEdge
  } = useAppStore();
  
  const [isAddingNode, setIsAddingNode] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [selectedEdges, setSelectedEdges] = useState<string[]>([]);
  const [isConnectingMode, setIsConnectingMode] = useState(false);
  const [draggedNode, setDraggedNode] = useState<any>(null);
  const [isDragging, setIsDragging] = useState(false);
  const reactFlowInstance = useReactFlow();
  const [isReactFlowReady, setIsReactFlowReady] = useState(false);
  const [lastClickTime, setLastClickTime] = useState(0);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    type: 'nodes' | 'edges';
    items: string[];
    show: boolean;
  }>({ type: 'nodes', items: [], show: false });

  // 获取当前用户可见的节点
  const visibleNodes = useMemo(() => getVisibleNodes(), [getVisibleNodes]);
  
  // 转换为ReactFlow格式的节点
  const reactFlowNodes: Node[] = useMemo(() => {
    return visibleNodes.map((node: FlowNodeType) => ({
      id: node.id,
      type: 'flowNode',
      position: node.position,
      data: node,
      draggable: isEditMode,
      selectable: true,
      deletable: isEditMode,
      selected: selectedNodes.includes(node.id)
    }));
  }, [visibleNodes, isEditMode, selectedNodes]);

  // 过滤边，只显示可见节点之间的连接
  const visibleEdges = useMemo(() => {
    const visibleNodeIds = new Set(visibleNodes.map(n => n.id));
    return storeEdges.filter(edge => 
      visibleNodeIds.has(edge.source) && visibleNodeIds.has(edge.target)
    );
  }, [storeEdges, visibleNodes]);

  const [nodes, setNodes, defaultOnNodesChange] = useNodesState([]);
  const [edges, setEdges, defaultOnEdgesChange] = useEdgesState([]);



  // 自定义节点变化处理函数，同步删除操作到store
  const onNodesChange = useCallback((changes: any[]) => {
    console.log('onNodesChange called with:', changes);

    // 处理删除操作
    const removeChanges = changes.filter(change => change.type === 'remove');
    if (removeChanges.length > 0 && isEditMode) {
      console.log('Processing node removals:', removeChanges);
      removeChanges.forEach(change => {
        console.log('Removing node from store:', change.id);
        deleteNode(change.id);
      });

      // 清除选中状态
      const removedIds = removeChanges.map(change => change.id);
      setSelectedNodes(prev => prev.filter(id => !removedIds.includes(id)));

      toast.success(`已删除 ${removeChanges.length} 个节点`, {
        duration: 2000
      });
    }

    // 应用默认的节点变化处理
    defaultOnNodesChange(changes);
  }, [defaultOnNodesChange, deleteNode, isEditMode, setSelectedNodes]);

  // 自定义边变化处理函数，同步删除操作到store
  const onEdgesChange = useCallback((changes: any[]) => {
    console.log('onEdgesChange called with:', changes);

    // 处理删除操作
    const removeChanges = changes.filter(change => change.type === 'remove');
    if (removeChanges.length > 0 && isEditMode) {
      console.log('Processing edge removals:', removeChanges);
      removeChanges.forEach(change => {
        console.log('Removing edge from store:', change.id);
        deleteEdge(change.id);
      });

      // 清除选中状态
      const removedIds = removeChanges.map(change => change.id);
      setSelectedEdges(prev => prev.filter(id => !removedIds.includes(id)));

      toast.success(`已删除 ${removeChanges.length} 条连线`, {
        duration: 2000
      });
    }

    // 应用默认的边变化处理
    defaultOnEdgesChange(changes);
  }, [defaultOnEdgesChange, deleteEdge, isEditMode, setSelectedEdges]);

  // 边点击事件处理
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation();
    console.log('Edge clicked:', {
      edgeId: edge.id,
      isEditMode,
      edgeSelectable: edge.selectable,
      edgeDeletable: edge.deletable,
      currentSelectedEdges: selectedEdges
    });

    if (isEditMode) {
      // 切换选中状态
      const isCurrentlySelected = selectedEdges.includes(edge.id);
      if (isCurrentlySelected) {
        // 取消选中
        setSelectedEdges([]);
        console.log('Deselected edge:', edge.id);
        toast.info('已取消选中连线', {
          duration: 1500,
          description: '连线已取消选中'
        });
      } else {
        // 选中连线时，清除节点选择
        setSelectedEdges([edge.id]);
        setSelectedNodes([]);
        console.log('Selected edge:', edge.id);
        toast.success('连线已选中', {
          duration: 1500,
          description: '点击删除按钮或按Delete键删除连线',
          action: {
            label: '删除',
            onClick: () => {
              deleteEdge(edge.id);
              setSelectedEdges([]);
              toast.success('连线已删除');
            }
          }
        });
      }
    } else {
      // 非编辑模式下的提示
      toast.info('请先进入编辑模式', {
        duration: 2000,
        description: '点击右上角的编辑按钮进入编辑模式'
      });
    }
  }, [isEditMode, selectedEdges, deleteEdge]);
  // 同步store中的节点到ReactFlow，处理新增和删除
  React.useEffect(() => {
    console.log('Syncing nodes from store to ReactFlow:', {
      storeNodesCount: reactFlowNodes.length,
      currentNodesCount: nodes.length
    });

    setNodes(currentNodes => {
      // 获取当前节点的位置信息
      const currentPositions = new Map(
        currentNodes.map(node => [node.id, node.position])
      );

      // 更新节点，保持已存在节点的位置
      return reactFlowNodes.map(storeNode => ({
        ...storeNode,
        position: currentPositions.get(storeNode.id) || storeNode.position
      }));
    });
  }, [reactFlowNodes, setNodes]);

  // 更新边的状态
  React.useEffect(() => {
    console.log('Updating edges:', {
      visibleEdgesCount: visibleEdges.length,
      selectedEdgesCount: selectedEdges.length
    });

    const updatedEdges = visibleEdges.map(edge => {
      const isSelected = selectedEdges.includes(edge.id);
      return {
        ...edge,
        selected: isSelected,
        selectable: true,
        deletable: isEditMode,
        interactionWidth: 30, // 增加点击区域到30px
        style: {
          ...edge.style,
          strokeWidth: isSelected ? 4 : 2, // 选中时更粗
          stroke: isSelected ? '#EF4444' : (edge.style?.stroke || '#6B7280'),
          strokeDasharray: isSelected ? '5,5' : undefined, // 选中时添加虚线效果
          filter: isSelected ? 'drop-shadow(0 0 6px rgba(239, 68, 68, 0.6))' : undefined // 选中时添加阴影
        },
        // 添加动画效果
        animated: isSelected ? true : (edge.animated || false),
        // 增强选中时的视觉效果
        markerEnd: {
          type: 'arrowclosed',
          color: isSelected ? '#EF4444' : '#6B7280',
          width: 20,
          height: 20
        }
      };
    });

    setEdges(updatedEdges);
  }, [visibleEdges, selectedEdges, isEditMode]);

  const onConnect = useCallback(
    (params: Connection) => {
      if (isEditMode && params.source && params.target) {
        // 添加到本地状态
        setEdges((eds) => addEdge({
          ...params,
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6B7280', strokeWidth: 2 }
        }, eds));
        
        // 添加到store
        storeAddEdge({
          source: params.source,
          target: params.target,
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6B7280', strokeWidth: 2 }
        });
        
        toast.success('连线已添加', {
          description: `已在节点 ${params.source} 和 ${params.target} 之间添加连线`,
          duration: 2000
        });
      }
    },
    [setEdges, isEditMode, storeAddEdge]
  );

  // 节点拖拽结束时更新store中的位置
  const onNodeDragStop = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (isEditMode) {
        // 更新store中节点位置
        updateNodePosition(node.id, node.position);
        toast.success('节点位置已保存到本地缓存', {
          description: `节点 "${node.data?.name || node.id}" 的位置已保存，刷新页面后会自动恢复`,
          duration: 3000
        });
        console.log('Node position saved to localStorage:', node.id, node.position);
      }
    },
    [isEditMode, updateNodePosition]
  );

  // 节点和边选择变化（仅处理节点选择，边选择由 onEdgeClick 处理）
  const onSelectionChange = useCallback(
    ({ nodes: selectedNodes }: { nodes: Node[], edges: Edge[] }) => {
      console.log('Selection changed (nodes only):', {
        isEditMode,
        selectedNodes: selectedNodes.map(n => n.id)
      });

      if (isEditMode) {
        setSelectedNodes(selectedNodes.map(node => node.id));
        // 不处理边选择，由 onEdgeClick 处理
        console.log('Updated node selection state:', {
          selectedNodeIds: selectedNodes.map(node => node.id)
        });
      }
    },
    [isEditMode]
  );

  // 显示删除确认对话框
  const showDeleteConfirmation = useCallback((type: 'nodes' | 'edges', items: string[]) => {
    setDeleteConfirmation({ type, items, show: true });
  }, []);

  // 直接删除连线（不带确认）
  const deleteEdgesDirectly = useCallback((edgeIds: string[]) => {
    // 获取要删除的连线信息
    const edgesToDelete = storeEdges.filter(edge => edgeIds.includes(edge.id));
    const edgeDescriptions = edgesToDelete.map(edge => {
      const sourceNode = visibleNodes.find(n => n.id === edge.source);
      const targetNode = visibleNodes.find(n => n.id === edge.target);
      return `${sourceNode?.name || edge.source} → ${targetNode?.name || edge.target}`;
    });

    edgeIds.forEach(edgeId => {
      deleteEdge(edgeId);
    });
    setSelectedEdges([]);

    toast.success(`已删除 ${edgeIds.length} 条连线`, {
      description: edgeDescriptions.length > 0 ? edgeDescriptions.join('、') : undefined,
      duration: 3000
    });
  }, [storeEdges, visibleNodes, deleteEdge]);

  // 确认删除操作
  const confirmDelete = useCallback(() => {
    const { type, items } = deleteConfirmation;

    if (type === 'nodes') {
      deleteNodesDirectly(items);
    } else if (type === 'edges') {
      deleteEdgesDirectly(items);
    }

    setDeleteConfirmation({ type: 'nodes', items: [], show: false });
  }, [deleteConfirmation, deleteNodesDirectly, deleteEdgesDirectly]);

  // 取消删除操作
  const cancelDelete = useCallback(() => {
    setDeleteConfirmation({ type: 'nodes', items: [], show: false });
  }, []);

  // 直接删除节点（不带确认）
  const deleteNodesDirectly = useCallback((nodeIds: string[]) => {
    // 获取要删除的节点信息
    const nodesToDelete = visibleNodes.filter(node => nodeIds.includes(node.id));
    const nodeNames = nodesToDelete.map(node => node.name).join('、');

    // 计算相关连线数量
    const relatedEdges = storeEdges.filter(edge =>
      nodeIds.includes(edge.source) || nodeIds.includes(edge.target)
    );

    nodeIds.forEach(nodeId => {
      deleteNode(nodeId);
    });
    setSelectedNodes([]);

    toast.success(`已删除 ${nodeIds.length} 个节点`, {
      description: `删除节点：${nodeNames}${relatedEdges.length > 0 ? `，同时删除了 ${relatedEdges.length} 条相关连线` : ''}`,
      duration: 3000
    });
  }, [visibleNodes, storeEdges, deleteNode]);

  // 删除选中的节点（带确认）
  const handleDeleteSelectedNodes = useCallback(() => {
    if (isEditMode && selectedNodes.length > 0) {
      if (selectedNodes.length > 1) {
        // 多个节点需要确认
        showDeleteConfirmation('nodes', selectedNodes);
      } else {
        // 单个节点直接删除
        deleteNodesDirectly(selectedNodes);
      }
    }
  }, [isEditMode, selectedNodes, showDeleteConfirmation, deleteNodesDirectly]);

  // 删除选中的连线（带确认）
  const handleDeleteSelectedEdges = useCallback(() => {
    console.log('handleDeleteSelectedEdges called:', {
      isEditMode,
      selectedEdgesLength: selectedEdges.length,
      selectedEdges,
      currentEdges: edges.map(e => ({ id: e.id, selected: e.selected }))
    });

    if (isEditMode && selectedEdges.length > 0) {
      console.log('Starting edge deletion process for edges:', selectedEdges);

      // 连线删除通常不需要确认，直接删除
      deleteEdgesDirectly(selectedEdges);
    } else {
      console.log('Edge deletion skipped:', {
        reason: !isEditMode ? 'Not in edit mode' : 'No edges selected',
        isEditMode,
        selectedEdgesLength: selectedEdges.length
      });
    }
  }, [isEditMode, selectedEdges, deleteEdgesDirectly]);

  // 开始拖拽添加节点
  const handleStartAddNode = useCallback(() => {
    if (isEditMode) {
      setIsAddingNode(true);
      toast.info('拖拽节点到画布上添加', {
        description: '将节点拖拽到想要的位置',
        duration: 3000
      });
    }
  }, [isEditMode]);

  // 直接添加节点到画布中心
  const handleAddNodeDirectly = useCallback(() => {
    if (isEditMode && reactFlowInstance) {
      // 获取画布中心位置
      const viewport = reactFlowInstance.getViewport();
      const centerX = (window.innerWidth / 2 - viewport.x) / viewport.zoom;
      const centerY = (window.innerHeight / 2 - viewport.y) / viewport.zoom;

      console.log('Adding node directly at center:', { x: centerX, y: centerY });

      const newNodeId = addNode({
        type: 'custom',
        name: '新节点',
        description: '自定义节点',
        position: { x: centerX, y: centerY },
        role: currentUser?.role || 'user'
      });

      if (newNodeId) {
        toast.success('节点添加成功', { duration: 2000 });
        console.log('Node added successfully:', newNodeId);
      }
    }
  }, [isEditMode, reactFlowInstance, addNode, currentUser]);

  // 拖拽开始
  const onDragStart = useCallback((event: React.DragEvent, nodeType: string) => {
    setIsDragging(true);
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  // 拖拽结束
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // 放置节点
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      setIsDragging(false);
      setIsAddingNode(false);

      const reactFlowBounds = event.currentTarget.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type) {
        return;
      }

      // 检查reactFlowInstance是否已初始化
      if (!reactFlowInstance) {
        console.error('ReactFlow instance not initialized');
        toast.error('画布未初始化，请稍后再试');
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNodeId = addNode({
        type: 'custom',
        name: '新节点',
        description: '自定义节点',
        status: 'processing',
        position,
        permissions: {
          viewRoles: ['admin', 'dispatcher'],
          editRoles: ['admin']
        },
        style: {
          backgroundColor: '#1E3A8A',
          borderColor: '#1E40AF',
          textColor: '#FFFFFF'
        },
        data: {
          count: 10
        }
      });

      // 立即更新本地节点状态
      const newNode = {
        id: newNodeId,
        type: 'flowNode' as const,
        position,
        data: {
          id: newNodeId,
          type: 'custom' as const,
          name: '新节点',
          description: '自定义节点',
          status: 'processing' as const,
          position,
          permissions: {
            viewRoles: ['admin', 'dispatcher'],
            editRoles: ['admin']
          },
          style: {
            backgroundColor: '#1E3A8A',
            borderColor: '#1E40AF',
            textColor: '#FFFFFF'
          },
          data: {
             count: 10
           }
        },
        draggable: true,
        selectable: true,
        deletable: true
      };
      
      setNodes(currentNodes => {
        const updatedNodes = [...currentNodes, newNode];
        console.log('Added new node to local state:', newNodeId, updatedNodes.length);
        return updatedNodes;
      });

      toast.success('新节点已添加', {
        description: '可以编辑节点名称和设置阈值',
        duration: 2000
      });
    },
    [reactFlowInstance, addNode, setNodes]
  );

  // 处理画布点击（检测双击）
  const onPaneClick = useCallback(
    (event: React.MouseEvent) => {
      const currentTime = Date.now();
      const timeDiff = currentTime - lastClickTime;

      if (timeDiff < 300) { // 300ms内的第二次点击视为双击
        console.log('Pane double clicked:', {
          isEditMode,
          hasReactFlowInstance: !!reactFlowInstance,
          isReactFlowReady
        });

        if (isEditMode) {
          // 检查reactFlowInstance是否已初始化
          if (!reactFlowInstance || !isReactFlowReady) {
            console.error('ReactFlow instance not ready');
            toast.error('画布未初始化，请稍后再试');
            return;
          }

          const reactFlowBounds = event.currentTarget.getBoundingClientRect();
          const position = reactFlowInstance.project({
            x: event.clientX - reactFlowBounds.left,
            y: event.clientY - reactFlowBounds.top
          });

          console.log('Adding node at position:', position);

          const newNodeId = addNode({
            type: 'custom',
            name: '新节点',
            description: '自定义节点',
            status: 'processing',
            position,
            permissions: {
              viewRoles: ['admin', 'dispatcher'],
              editRoles: ['admin']
            },
            style: {
              backgroundColor: '#1E3A8A',
              borderColor: '#1E40AF',
              textColor: '#FFFFFF'
            },
            data: {
              count: 10
            }
          });

          console.log('Added new node via double-click:', newNodeId);

          toast.success('新节点已添加', {
            description: '双击画布空白处可以添加节点',
            duration: 2000
          });
        } else {
          console.log('Not in edit mode, ignoring double click');
        }
      }

      setLastClickTime(currentTime);
    },
    [isEditMode, addNode, reactFlowInstance, isReactFlowReady, lastClickTime]
  );

  // ReactFlow 初始化回调
  const onInit = useCallback((instance: any) => {
    console.log('ReactFlow initialized:', !!instance);
    setIsReactFlowReady(true);

    // 添加全局调试函数
    (window as any).testFlowFunctions = {
      selectFirstEdge: () => {
        if (visibleEdges.length > 0) {
          const firstEdge = visibleEdges[0];
          console.log('Testing: selecting first edge:', firstEdge.id);
          setSelectedEdges([firstEdge.id]);
          setSelectedNodes([]);
        } else {
          console.log('No edges available');
        }
      },
      addNodeAtCenter: () => {
        if (instance && isEditMode) {
          const position = { x: 300, y: 200 };
          console.log('Testing: adding node at center');
          const newNodeId = addNode({
            type: 'custom',
            name: '测试节点',
            description: '测试节点',
            status: 'processing',
            position,
            permissions: {
              viewRoles: ['admin', 'dispatcher'],
              editRoles: ['admin']
            },
            style: {
              backgroundColor: '#1E3A8A',
              borderColor: '#1E40AF',
              textColor: '#FFFFFF'
            },
            data: { count: 10 }
          });
          console.log('Test node added:', newNodeId);
        } else {
          console.log('Cannot add node: not in edit mode or instance not ready');
        }
      }
    };

    console.log('Debug functions added to window.testFlowFunctions');
  }, [visibleEdges, isEditMode, addNode]);

  // 键盘事件处理
  const onKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (isEditMode && event.key === 'Delete') {
        if (selectedNodes.length > 0) {
          handleDeleteSelectedNodes();
        } else if (selectedEdges.length > 0) {
          handleDeleteSelectedEdges();
        }
      }
    },
    [isEditMode, selectedNodes, selectedEdges, handleDeleteSelectedNodes, handleDeleteSelectedEdges]
  );

  // 监听键盘事件
  React.useEffect(() => {
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, [onKeyDown]);

  const proOptions = {
    hideAttribution: true
  };

  return (
    <div className={`w-full h-full bg-gray-50 ${className}`}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDragStop={onNodeDragStop}
        onSelectionChange={onSelectionChange}
        onEdgeClick={onEdgeClick}
        onPaneClick={onPaneClick}

        onDrop={onDrop}
        onDragOver={onDragOver}
        onInit={onInit}
        nodeTypes={nodeTypes}
        connectionMode={ConnectionMode.Loose}
        fitView
        fitViewOptions={{
          padding: 0.5,
          includeHiddenNodes: false,
          minZoom: 0.3,
          maxZoom: 2.0
        }}
        proOptions={proOptions}
        className="bg-gray-50"
        multiSelectionKeyCode="Shift"
        deleteKeyCode="Delete"
      >
        <Controls 
          className="bg-white shadow-lg border border-gray-200 rounded-lg"
          showZoom={true}
          showFitView={true}
          showInteractive={false}
        />
        
        <MiniMap 
          className="bg-white shadow-lg border border-gray-200 rounded-lg"
          nodeColor={(node) => {
            const nodeData = node.data as FlowNodeType;
            return nodeData.style?.backgroundColor || '#3B82F6';
          }}
          nodeStrokeWidth={2}
          zoomable
          pannable
        />
        
        <Background 
          variant={BackgroundVariant.Dots} 
          gap={20} 
          size={1} 
          color="#E5E7EB"
        />
        
        {/* 节点状态信息 - 右上角横向展示 */}
        <div className="absolute top-4 right-4 bg-white shadow-lg rounded-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-4 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded" />
              <span>已完成</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded" />
              <span>处理中</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded" />
              <span>警告</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded" />
              <span>异常</span>
            </div>
          </div>
        </div>
        
        {/* 用户角色和编辑模式指示器 */}
        <div className="absolute top-4 left-4 bg-white shadow-lg rounded-lg p-3 border border-gray-200">
          <div className="flex items-center justify-between space-x-3">
            <div className="flex items-center space-x-2 text-sm">
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${
                  currentUser?.role === 'admin' ? 'bg-red-500' :
                  currentUser?.role === 'dispatcher' ? 'bg-blue-500' : 'bg-green-500'
                }`} />
                <span className="font-medium">
                  {currentUser?.role === 'admin' ? '管理员' :
                   currentUser?.role === 'dispatcher' ? '调度员' : '客户'}
                </span>
              </div>
              {isEditMode && (
                <div className="flex items-center space-x-1 text-orange-600">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
                  <span className="text-xs">编辑模式</span>
                </div>
              )}
            </div>
            {/* 编辑模式控制按钮 */}
            {isEditMode && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleAddNodeDirectly}
                  className="flex items-center space-x-1 px-2 py-1 text-xs bg-green-100 text-green-700 hover:bg-green-200 rounded transition-colors"
                  title="直接在画布中心添加节点"
                >
                  <Plus className="w-3 h-3" />
                  <span>新增节点</span>
                </button>

                <button
                  onClick={handleStartAddNode}
                  className={`flex items-center space-x-1 px-2 py-1 text-xs rounded transition-colors ${
                    isAddingNode
                      ? 'bg-orange-100 text-orange-700 hover:bg-orange-200'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                  title="点击后拖拽节点到画布上"
                >
                  <Plus className="w-3 h-3" />
                  <span>{isAddingNode ? '拖拽节点' : '拖拽添加'}</span>
                </button>
                
                {selectedNodes.length > 0 && (
                  <button
                    onClick={handleDeleteSelectedNodes}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 rounded transition-colors"
                    title="删除选中的节点"
                  >
                    <Trash2 className="w-3 h-3" />
                    <span>删除节点({selectedNodes.length})</span>
                  </button>
                )}
                
                {selectedEdges.length > 0 && (
                  <button
                    onClick={handleDeleteSelectedEdges}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 rounded transition-colors"
                    title="删除选中的连线"
                  >
                    <Unlink className="w-3 h-3" />
                    <span>删除连线({selectedEdges.length})</span>
                  </button>
                )}




                
                <button
                  onClick={() => {
                    resetNodePositions();
                    toast.success('节点位置已重置', {
                      description: '所有节点已恢复到默认位置，本地缓存已清除',
                      duration: 3000
                    });
                  }}
                  className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                  title="重置所有节点到默认位置"
                >
                  <RotateCcw className="w-3 h-3" />
                  <span>重置位置</span>
                </button>
              </div>
            )}
          </div>
        </div>
        
        {/* 可拖拽的节点模板 */}
        {isEditMode && isAddingNode && (
          <div className="absolute bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 border border-gray-200">
            <div className="text-xs font-medium text-gray-700 mb-3">拖拽节点到画布</div>
            <div 
              className="w-24 h-16 bg-blue-600 rounded-lg shadow-lg cursor-grab active:cursor-grabbing flex flex-col items-center justify-center text-white text-xs font-medium border-2 border-blue-700 hover:bg-blue-700 transition-colors"
              draggable
              onDragStart={(event) => onDragStart(event, 'custom')}
            >
              <div className="text-center">
                <div className="font-bold">新节点</div>
                <div className="text-xs opacity-80">运单: 10</div>
              </div>
            </div>
            <div className="text-xs text-gray-500 mt-2 text-center">
              拖拽到画布上添加
            </div>
            <button
              onClick={() => setIsAddingNode(false)}
              className="mt-2 w-full text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              取消添加
            </button>
          </div>
        )}
        
        {/* 操作说明 */}
        {isEditMode && !isAddingNode && (
          <div className="absolute bottom-4 left-4 bg-white shadow-lg rounded-lg p-3 border border-gray-200">
            <div className="text-xs font-medium text-gray-700 mb-2">编辑操作</div>
            <div className="space-y-1 text-xs text-gray-600">
              <div>• 双击画布空白处添加节点</div>
              <div>• 拖拽节点移动位置</div>
              <div>• 拖拽节点边缘创建连线</div>
              <div>• 点击连线后可删除</div>
              <div>• 按Delete键删除选中项</div>
              <div>• 双击节点编辑名称和阈值</div>
            </div>
          </div>
        )}
      </ReactFlow>

      {/* 删除确认对话框 */}
      {deleteConfirmation.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <Trash2 className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  确认删除{deleteConfirmation.type === 'nodes' ? '节点' : '连线'}
                </h3>
                <p className="text-sm text-gray-500">
                  此操作无法撤销
                </p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-700">
                您确定要删除 {deleteConfirmation.items.length} 个
                {deleteConfirmation.type === 'nodes' ? '节点' : '连线'}吗？
              </p>
              {deleteConfirmation.type === 'nodes' && (
                <p className="text-sm text-gray-500 mt-2">
                  删除节点将同时删除所有相关的连线。
                </p>
              )}
            </div>

            <div className="flex space-x-3 justify-end">
              <button
                onClick={cancelDelete}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                取消
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};